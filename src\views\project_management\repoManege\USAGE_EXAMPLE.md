# Word 报告生成功能使用示例

## 功能演示

### 1. 界面展示

在 ProjectStatus 组件的筛选栏中，现在有两个按钮：
- **刷新数据**: 重新加载项目数据
- **生成报告**: 生成包含图表和表格的 Word 报告

### 2. 使用步骤

#### 步骤 1: 选择时间范围
```vue
<!-- 年份选择 -->
<el-select v-model="selectedYear" placeholder="选择年份">
  <el-option v-for="y in years" :key="y" :label="y + '年'" :value="y" />
</el-select>

<!-- 季度选择 -->
<el-select v-model="selectedQuarter" placeholder="选择季度">
  <el-option label="全部季度" value="" />
  <el-option label="第1季度" value="1" />
  <el-option label="第2季度" value="2" />
  <el-option label="第3季度" value="3" />
  <el-option label="第4季度" value="4" />
</el-select>
```

#### 步骤 2: 点击生成报告
```vue
<el-button
  type="success"
  size="small"
  @click="generateWordReport"
  :loading="reportLoading"
>
  <el-icon><i-ep-document /></el-icon>
  生成报告
</el-button>
```

#### 步骤 3: 等待生成完成
- 系统会显示"正在生成报告，请稍候..."的提示
- 生成过程中按钮会显示加载状态
- 完成后会自动下载 Word 文档

### 3. 生成的报告内容

#### 报告结构
1. **标题页**
   - 项目状态报告 - 2024年第1季度

2. **项目概述**
   - 统计数据摘要
   - 例如："2024年第1季度期间，累计项目数 15 个，完成项目数 8 个，待完成项目数 7 个，总体完成率 53%。"

3. **项目数量趋势图**
   - 折线图显示各月份项目数量变化
   - 图片格式嵌入到 Word 文档中

4. **项目完成状态分布**
   - 饼图显示已完成和未完成项目的比例
   - 图片格式嵌入到 Word 文档中

5. **项目详细列表**
   - 表格形式展示所有项目信息
   - 包含：项目名称、负责人、开始日期、结束日期、状态

### 4. 技术实现要点

#### 图表捕获
```typescript
// 捕获折线图
const lineChartImage = await captureChart(chartRef.value || null);

// 捕获饼图
const pieChartElement = document.querySelector('.chart') as HTMLElement;
const pieChartImage = await captureChart(pieChartElement);
```

#### Word 文档生成
```typescript
const doc = new Document({
  sections: [{
    properties: {},
    children: [
      // 标题
      new Paragraph({
        children: [new TextRun({
          text: `项目状态报告 - ${selectedYear.value}年${selectedQuarter.value ? `第${selectedQuarter.value}季度` : ''}`,
          bold: true,
          size: 32,
        })],
        alignment: AlignmentType.CENTER,
        heading: HeadingLevel.HEADING_1,
      }),
      
      // 图表
      new Paragraph({
        children: [new ImageRun({
          data: lineChartImage,
          transformation: { width: 600, height: 300 },
          type: "png",
        })],
        alignment: AlignmentType.CENTER,
      }),
      
      // 表格
      createProjectTable(),
    ],
  }],
});
```

### 5. 错误处理

系统包含完善的错误处理机制：
- 图表捕获失败时会跳过相应图片
- 数据为空时会显示默认值
- 生成失败时会显示错误提示

### 6. 文件下载

生成的文件会自动下载，文件名格式：
`项目状态报告_2024年第1季度_2024-08-15.docx`

## 注意事项

1. **数据准备**: 确保页面数据已完全加载
2. **图表渲染**: 等待图表完全渲染后再生成报告
3. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Edge）
4. **文件大小**: 包含图片的 Word 文档可能较大（通常 1-5MB）
