# ProjectStatus 组件 Word 报告生成功能

## 功能概述

基于 ProjectStatus.vue 组件，新增了生成包含 ECharts 图表和表格内容的 Word 报告功能。

## 主要特性

1. **图表截图**: 自动捕获页面中的 ECharts 图表（折线图和饼图）并转换为图片
2. **表格数据**: 将 el-table 中的项目数据转换为 Word 表格格式
3. **完整报告**: 生成包含概述信息、图表和详细数据的完整 Word 文档

## 技术实现

### 依赖库
- `docx`: 用于生成 Word 文档
- `html2canvas`: 用于将 DOM 元素转换为图片
- `file-saver`: 用于下载生成的文件

### 核心功能

#### 1. 图表捕获 (`captureChart`)
```typescript
const captureChart = async (element: HTMLElement | null): Promise<Uint8Array | null>
```
- 使用 html2canvas 将图表元素转换为 PNG 图片
- 返回 Uint8Array 格式的图片数据

#### 2. 表格生成 (`createProjectTable`)
```typescript
const createProjectTable = (): Table
```
- 创建包含项目数据的 Word 表格
- 包含项目名称、负责人、开始日期、结束日期、状态等字段

#### 3. 报告生成 (`generateWordReport`)
```typescript
const generateWordReport = async (): Promise<void>
```
- 整合所有数据和图表
- 生成完整的 Word 文档
- 自动下载生成的报告

## 使用方法

1. 在 ProjectStatus 组件中，点击"生成报告"按钮
2. 系统会自动：
   - 捕获当前页面的图表
   - 收集表格数据
   - 生成 Word 文档
   - 自动下载文件

## 报告内容结构

1. **标题**: 项目状态报告 - [年份][季度]
2. **项目概述**: 统计数据摘要
3. **项目数量趋势**: 折线图
4. **项目完成状态分布**: 饼图  
5. **项目详细列表**: 数据表格

## 文件命名规则

生成的文件名格式：`项目状态报告_[年份]年[季度]_[日期].docx`

例如：`项目状态报告_2024年第1季度_2024-08-15.docx`

## 注意事项

1. 确保页面数据已加载完成再生成报告
2. 图表需要完全渲染后才能正确捕获
3. 生成过程中请勿切换页面或最小化浏览器窗口
4. 大量数据可能需要较长的生成时间

## 测试

可以使用 `WordReportTest.vue` 组件进行功能测试。
