<template>
  <div class="word-report-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Word 报告生成测试</span>
        </div>
      </template>
      
      <div class="test-content">
        <p>这是一个测试页面，用于验证 ProjectStatus 组件的 Word 报告生成功能。</p>
        
        <el-divider />
        
        <!-- 嵌入 ProjectStatus 组件 -->
        <ProjectStatus :defaultYear="2024" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import ProjectStatus from './ProjectStatus.vue';
</script>

<style scoped>
.word-report-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  padding: 20px 0;
}
</style>
