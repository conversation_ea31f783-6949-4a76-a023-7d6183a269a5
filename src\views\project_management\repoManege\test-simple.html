<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word 报告生成测试</title>
    <script src="https://unpkg.com/docx@9.5.1/build/index.js"></script>
    <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
</head>
<body>
    <h1>Word 报告生成功能测试</h1>
    <button onclick="testWordGeneration()">测试生成 Word 文档</button>
    
    <script>
        async function testWordGeneration() {
            try {
                console.log('开始测试 Word 文档生成...');
                
                // 使用 docx 库创建文档
                const { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, WidthType, AlignmentType, HeadingLevel } = docx;
                
                const doc = new Document({
                    sections: [{
                        properties: {},
                        children: [
                            // 标题
                            new Paragraph({
                                children: [
                                    new TextRun({
                                        text: "项目状态报告测试 - 2024年",
                                        bold: true,
                                        size: 32,
                                    }),
                                ],
                                alignment: AlignmentType.CENTER,
                                heading: HeadingLevel.HEADING_1,
                            }),
                            
                            // 空行
                            new Paragraph({ children: [new TextRun("")] }),
                            
                            // 概述
                            new Paragraph({
                                children: [
                                    new TextRun({
                                        text: "项目概述",
                                        bold: true,
                                        size: 24,
                                    }),
                                ],
                                heading: HeadingLevel.HEADING_2,
                            }),
                            
                            new Paragraph({
                                children: [
                                    new TextRun({
                                        text: "2024年期间，累计项目数 10 个，完成项目数 6 个，待完成项目数 4 个，总体完成率 60%。",
                                        size: 20,
                                    }),
                                ],
                            }),
                            
                            // 空行
                            new Paragraph({ children: [new TextRun("")] }),
                            
                            // 表格
                            new Paragraph({
                                children: [
                                    new TextRun({
                                        text: "项目详细列表",
                                        bold: true,
                                        size: 24,
                                    }),
                                ],
                                heading: HeadingLevel.HEADING_2,
                            }),
                            
                            // 创建测试表格
                            new Table({
                                rows: [
                                    // 表头
                                    new TableRow({
                                        children: [
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun({ text: "项目名称", bold: true })] })],
                                                width: { size: 25, type: WidthType.PERCENTAGE },
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun({ text: "负责人", bold: true })] })],
                                                width: { size: 15, type: WidthType.PERCENTAGE },
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun({ text: "状态", bold: true })] })],
                                                width: { size: 20, type: WidthType.PERCENTAGE },
                                            }),
                                        ],
                                    }),
                                    // 数据行
                                    new TableRow({
                                        children: [
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("测试项目1")] })],
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("张三")] })],
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("已完成")] })],
                                            }),
                                        ],
                                    }),
                                    new TableRow({
                                        children: [
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("测试项目2")] })],
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("李四")] })],
                                            }),
                                            new TableCell({
                                                children: [new Paragraph({ children: [new TextRun("进行中")] })],
                                            }),
                                        ],
                                    }),
                                ],
                                width: {
                                    size: 100,
                                    type: WidthType.PERCENTAGE,
                                },
                            }),
                        ],
                    }],
                });
                
                // 生成并下载文档
                const blob = await Packer.toBlob(doc);
                const fileName = `测试报告_${new Date().toISOString().slice(0, 10)}.docx`;
                saveAs(blob, fileName);
                
                console.log('Word 文档生成成功！');
                alert('Word 文档生成成功！');
                
            } catch (error) {
                console.error('生成失败:', error);
                alert('生成失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
