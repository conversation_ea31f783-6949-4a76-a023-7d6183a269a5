/**
 * Word 报告生成功能测试脚本
 * 
 * 这个脚本可以在浏览器控制台中运行，用于测试 Word 报告生成功能
 */

// 测试函数：检查必要的依赖是否已加载
function checkDependencies() {
  console.log('🔍 检查依赖...');
  
  const dependencies = [
    { name: 'html2canvas', check: () => typeof html2canvas !== 'undefined' },
    { name: 'docx', check: () => typeof docx !== 'undefined' },
    { name: 'file-saver', check: () => typeof saveAs !== 'undefined' },
  ];
  
  dependencies.forEach(dep => {
    const available = dep.check();
    console.log(`${available ? '✅' : '❌'} ${dep.name}: ${available ? '已加载' : '未找到'}`);
  });
}

// 测试函数：检查页面元素是否存在
function checkPageElements() {
  console.log('🔍 检查页面元素...');
  
  const elements = [
    { name: '折线图容器', selector: '.chart-container' },
    { name: '饼图容器', selector: '.chart' },
    { name: '项目表格', selector: '.el-table' },
    { name: '生成报告按钮', selector: 'button:contains("生成报告")' },
  ];
  
  elements.forEach(element => {
    const found = document.querySelector(element.selector);
    console.log(`${found ? '✅' : '❌'} ${element.name}: ${found ? '已找到' : '未找到'}`);
  });
}

// 测试函数：模拟图表捕获
async function testChartCapture() {
  console.log('🔍 测试图表捕获...');
  
  try {
    // 测试折线图捕获
    const lineChartElement = document.querySelector('.chart-container');
    if (lineChartElement) {
      console.log('📊 尝试捕获折线图...');
      const canvas = await html2canvas(lineChartElement, {
        backgroundColor: null,
        scale: 1,
        useCORS: true,
        allowTaint: true,
      });
      console.log('✅ 折线图捕获成功', { width: canvas.width, height: canvas.height });
    } else {
      console.log('❌ 未找到折线图元素');
    }
    
    // 测试饼图捕获
    const pieChartElement = document.querySelector('.chart');
    if (pieChartElement) {
      console.log('🥧 尝试捕获饼图...');
      const canvas = await html2canvas(pieChartElement, {
        backgroundColor: null,
        scale: 1,
        useCORS: true,
        allowTaint: true,
      });
      console.log('✅ 饼图捕获成功', { width: canvas.width, height: canvas.height });
    } else {
      console.log('❌ 未找到饼图元素');
    }
    
  } catch (error) {
    console.error('❌ 图表捕获失败:', error);
  }
}

// 测试函数：检查数据状态
function checkDataState() {
  console.log('🔍 检查数据状态...');
  
  // 检查是否有 Vue 实例
  const vueApp = document.querySelector('#app').__vue__;
  if (vueApp) {
    console.log('✅ Vue 应用已找到');
    
    // 尝试获取项目数据
    const tables = document.querySelectorAll('.el-table tbody tr');
    console.log(`📊 找到 ${tables.length} 行项目数据`);
    
    // 检查统计卡片
    const statCards = document.querySelectorAll('.card-count');
    console.log(`📈 找到 ${statCards.length} 个统计卡片`);
    
  } else {
    console.log('❌ 未找到 Vue 应用实例');
  }
}

// 主测试函数
async function runWordReportTest() {
  console.log('🚀 开始 Word 报告生成功能测试');
  console.log('='.repeat(50));
  
  // 1. 检查依赖
  checkDependencies();
  console.log('');
  
  // 2. 检查页面元素
  checkPageElements();
  console.log('');
  
  // 3. 检查数据状态
  checkDataState();
  console.log('');
  
  // 4. 测试图表捕获
  await testChartCapture();
  console.log('');
  
  console.log('✅ 测试完成！');
  console.log('💡 如果所有检查都通过，可以尝试点击"生成报告"按钮');
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.runWordReportTest = runWordReportTest;
  window.checkDependencies = checkDependencies;
  window.checkPageElements = checkPageElements;
  window.testChartCapture = testChartCapture;
  window.checkDataState = checkDataState;
  
  console.log('📝 Word 报告测试脚本已加载');
  console.log('💡 在控制台中运行 runWordReportTest() 开始测试');
}

// 如果在 Node.js 环境中
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runWordReportTest,
    checkDependencies,
    checkPageElements,
    testChartCapture,
    checkDataState,
  };
}
